from database import db  # Importe l'instance 'db' depuis database.py
from datetime import datetime
import pytz

class Product(db.Model):
    """
    Modèle représentant un produit dans la base de données.
    created_at sera stocké en UTC.
    """
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    price = db.Column(db.Float, nullable=False)
    # created_at est stocké en UTC avec information de fuseau horaire
    # SQLAlchemy stocke les objets datetime "aware" correctement avec timezone=True
    created_at = db.Column(db.DateTime(timezone=True), nullable=False)

    def __init__(self, name, price, created_at=None):
        """
        Initialise un nouveau produit.
        Si created_at n'est pas fourni, utilise l'heure actuelle en UTC.
        Si created_at est fourni, s'assure qu'il est en UTC.
        """
        self.name = name
        self.price = price

        if created_at is None:
            # Utiliser l'heure actuelle en UTC
            self.created_at = datetime.now(pytz.utc)
        else:
            # S'assurer que la date est en UTC
            if created_at.tzinfo is None:
                # Si la date est naive, l'interpréter comme UTC
                self.created_at = pytz.utc.localize(created_at)
            elif created_at.tzinfo != pytz.utc:
                # Si la date a un fuseau horaire différent, la convertir en UTC
                self.created_at = created_at.astimezone(pytz.utc)
            else:
                # La date est déjà en UTC
                self.created_at = created_at

    def __repr__(self):
        return f'<Product {self.name}>'