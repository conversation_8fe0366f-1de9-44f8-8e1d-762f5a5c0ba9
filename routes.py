from flask import render_template, request, redirect, url_for
from datetime import datetime
import pytz # Importé ici car utilisé directement dans la route

# Cette fonction prend l'application Flask, l'objet db, le modèle Product et pytz
# pour éviter les importations circulaires et rendre les modules plus indépendants.
def init_routes(app, db, Product, pytz):

    @app.route('/', methods=['GET', 'POST'])
    def index():
        # Récupérer le fuseau horaire sélectionné (par défaut UTC)
        selected_timezone = request.args.get('timezone', 'UTC')

        if request.method == 'POST':
            try:
                name = request.form['name']
                price = float(request.form['price'])
                # La date et l'heure envoyées par le formulaire sont déjà censées être dans le
                # fuseau horaire choisi par l'utilisateur. Nous devons les convertir en UTC.
                datetime_str = request.form['datetime'] # Ex: '2023-10-27T14:30'
                timezone_str = request.form['timezone'] # Ex: 'Europe/Paris'

                # 1. Créer un objet datetime "naïf" à partir de l'input du formulaire.
                naive_dt = datetime.strptime(datetime_str, '%Y-%m-%dT%H:%M')

                # 2. Obtenir l'objet fuseau horaire sélectionné.
                selected_tz = pytz.timezone(timezone_str)

                # 3. Localiser l'objet datetime "naïf" avec le fuseau horaire sélectionné,
                #    le rendant "aware" de son fuseau horaire d'origine.
                aware_dt_local = selected_tz.localize(naive_dt)

                # 4. Convertir cette date/heure "aware" en UTC.
                aware_dt_utc = aware_dt_local.astimezone(pytz.utc)

                new_product = Product(name=name, price=price, created_at=aware_dt_utc)
                db.session.add(new_product)
                db.session.commit()

                # Rediriger avec le fuseau horaire sélectionné pour maintenir l'affichage
                return redirect(url_for('index', timezone=timezone_str))
            except Exception as e:
                print(f"Erreur lors de l'ajout du produit: {e}")
                return redirect(url_for('index', timezone=selected_timezone))

        # Récupérer tous les produits (stockés en UTC)
        products = Product.query.order_by(Product.created_at.desc()).all()

        # Convertir les dates des produits pour l'affichage selon le fuseau horaire sélectionné
        display_products = []
        if selected_timezone != 'UTC':
            try:
                display_tz = pytz.timezone(selected_timezone)
                for product in products:
                    # Créer une copie du produit avec la date convertie pour l'affichage
                    display_product = {
                        'id': product.id,
                        'name': product.name,
                        'price': product.price,
                        'created_at': product.created_at,  # UTC pour JavaScript
                        'display_time': product.created_at.astimezone(display_tz)  # Converti pour affichage initial
                    }
                    display_products.append(display_product)
            except:
                # En cas d'erreur, utiliser les produits originaux
                display_products = [{'id': p.id, 'name': p.name, 'price': p.price, 'created_at': p.created_at, 'display_time': p.created_at} for p in products]
        else:
            display_products = [{'id': p.id, 'name': p.name, 'price': p.price, 'created_at': p.created_at, 'display_time': p.created_at} for p in products]

        # Liste des fuseaux horaires communs pour le template
        common_timezones = sorted(pytz.common_timezones)

        # Rendre le template avec le fuseau horaire sélectionné
        return render_template('index.html',
                             products=display_products,
                             timezones=common_timezones,
                             selected_timezone=selected_timezone)