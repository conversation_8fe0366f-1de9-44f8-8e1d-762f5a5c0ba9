#!/usr/bin/env python3
"""
Script de test pour vérifier la fonctionnalité de gestion des fuseaux horaires.
Ce script teste que les produits sont correctement stockés en UTC et affichés
selon le fuseau horaire sélectionné.
"""

import os
import sys
import pytz
from datetime import datetime
from flask import Flask

# Ajouter le répertoire courant au path pour les imports
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from database import db
from models import Product

def test_timezone_functionality():
    """Test de la fonctionnalité de gestion des fuseaux horaires."""
    
    # Configuration de l'application de test
    app = Flask(__name__)
    basedir = os.path.abspath(os.path.dirname(__file__))
    app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///' + os.path.join(basedir, 'test_products.db')
    app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False
    
    db.init_app(app)
    
    with app.app_context():
        # Créer les tables
        db.create_all()
        
        # Nettoyer les données existantes
        Product.query.delete()
        db.session.commit()
        
        print("=== Test de la gestion des fuseaux horaires ===\n")
        
        # Test 1: Créer un produit avec une date en fuseau horaire Paris
        print("Test 1: Création d'un produit avec fuseau horaire Europe/Paris")
        paris_tz = pytz.timezone('Europe/Paris')
        naive_dt = datetime(2024, 1, 15, 14, 30, 0)  # 14h30 heure locale
        aware_dt_paris = paris_tz.localize(naive_dt)
        aware_dt_utc = aware_dt_paris.astimezone(pytz.utc)
        
        product1 = Product(name="Produit Paris", price=25.50, created_at=aware_dt_utc)
        db.session.add(product1)
        
        print(f"  - Heure locale Paris: {aware_dt_paris}")
        print(f"  - Heure UTC stockée: {aware_dt_utc}")
        
        # Test 2: Créer un produit avec une date en fuseau horaire New York
        print("\nTest 2: Création d'un produit avec fuseau horaire America/New_York")
        ny_tz = pytz.timezone('America/New_York')
        naive_dt2 = datetime(2024, 1, 15, 9, 15, 0)  # 9h15 heure locale
        aware_dt_ny = ny_tz.localize(naive_dt2)
        aware_dt_utc2 = aware_dt_ny.astimezone(pytz.utc)
        
        product2 = Product(name="Produit New York", price=35.75, created_at=aware_dt_utc2)
        db.session.add(product2)
        
        print(f"  - Heure locale New York: {aware_dt_ny}")
        print(f"  - Heure UTC stockée: {aware_dt_utc2}")
        
        # Test 3: Créer un produit avec une date en fuseau horaire Tokyo
        print("\nTest 3: Création d'un produit avec fuseau horaire Asia/Tokyo")
        tokyo_tz = pytz.timezone('Asia/Tokyo')
        naive_dt3 = datetime(2024, 1, 15, 23, 45, 0)  # 23h45 heure locale
        aware_dt_tokyo = tokyo_tz.localize(naive_dt3)
        aware_dt_utc3 = aware_dt_tokyo.astimezone(pytz.utc)
        
        product3 = Product(name="Produit Tokyo", price=45.00, created_at=aware_dt_utc3)
        db.session.add(product3)
        
        print(f"  - Heure locale Tokyo: {aware_dt_tokyo}")
        print(f"  - Heure UTC stockée: {aware_dt_utc3}")
        
        db.session.commit()
        
        # Test 4: Vérifier que toutes les dates sont stockées en UTC
        print("\n=== Vérification du stockage en UTC ===")
        products = Product.query.all()
        for product in products:
            print(f"  - {product.name}: {product.created_at}")
            # Vérifier que la date est bien en UTC
            if product.created_at.tzinfo is None:
                print(f"    ⚠️  ERREUR: La date n'a pas d'information de fuseau horaire!")
            elif str(product.created_at.tzinfo) == 'UTC' or product.created_at.utcoffset() == pytz.utc.utcoffset(product.created_at):
                print(f"    ✅ Date correctement stockée en UTC")
            else:
                print(f"    ⚠️  ERREUR: La date n'est pas en UTC! Fuseau: {product.created_at.tzinfo}")
        
        # Test 5: Affichage dans différents fuseaux horaires
        print("\n=== Test d'affichage dans différents fuseaux horaires ===")
        timezones_to_test = ['Europe/Paris', 'America/New_York', 'Asia/Tokyo', 'UTC']
        
        for tz_name in timezones_to_test:
            print(f"\n--- Affichage en {tz_name} ---")
            tz = pytz.timezone(tz_name) if tz_name != 'UTC' else pytz.utc
            
            for product in products:
                local_time = product.created_at.astimezone(tz)
                print(f"  - {product.name}: {local_time.strftime('%Y-%m-%d %H:%M:%S %Z')}")
        
        print("\n=== Test terminé avec succès! ===")
        print("\nRésumé:")
        print("✅ Les produits sont stockés en UTC dans la base de données")
        print("✅ Les dates peuvent être affichées dans n'importe quel fuseau horaire")
        print("✅ La conversion entre fuseaux horaires fonctionne correctement")

if __name__ == "__main__":
    test_timezone_functionality()
