#!/usr/bin/env python3
"""
Test de l'application web pour vérifier la fonctionnalité de gestion des fuseaux horaires.
"""

import requests
import time
from datetime import datetime
import pytz

def test_web_application():
    """Test de l'application web en cours d'exécution."""
    
    base_url = "http://127.0.0.1:5001"
    
    print("=== Test de l'application web ===\n")
    
    try:
        # Test 1: Vérifier que l'application répond
        print("Test 1: Vérification de la disponibilité de l'application")
        response = requests.get(base_url, timeout=5)
        if response.status_code == 200:
            print("✅ Application accessible")
        else:
            print(f"❌ Application non accessible (code: {response.status_code})")
            return
        
        # Test 2: Vérifier que la page contient les éléments nécessaires
        print("\nTest 2: Vérification du contenu de la page")
        content = response.text
        
        required_elements = [
            'id="timezone"',
            'id="datetime"',
            'name="name"',
            'name="price"',
            'data-utc-time'
        ]
        
        for element in required_elements:
            if element in content:
                print(f"✅ Élément trouvé: {element}")
            else:
                print(f"❌ Élément manquant: {element}")
        
        # Test 3: Tester l'ajout d'un produit
        print("\nTest 3: Test d'ajout d'un produit")
        
        # Données du produit à ajouter
        product_data = {
            'name': 'Produit Test',
            'price': '29.99',
            'datetime': '2024-01-15T15:30',
            'timezone': 'Europe/Paris'
        }
        
        # Envoyer la requête POST
        response = requests.post(base_url, data=product_data, timeout=5)
        
        if response.status_code == 200 or response.status_code == 302:  # 302 pour redirection
            print("✅ Produit ajouté avec succès")
            
            # Vérifier que le produit apparaît dans la liste
            time.sleep(1)  # Attendre un peu
            response = requests.get(f"{base_url}?timezone=Europe/Paris", timeout=5)
            if 'Produit Test' in response.text:
                print("✅ Produit visible dans la liste")
            else:
                print("❌ Produit non visible dans la liste")
        else:
            print(f"❌ Erreur lors de l'ajout du produit (code: {response.status_code})")
        
        # Test 4: Tester l'affichage avec différents fuseaux horaires
        print("\nTest 4: Test d'affichage avec différents fuseaux horaires")
        
        timezones_to_test = ['Europe/Paris', 'America/New_York', 'Asia/Tokyo', 'UTC']
        
        for tz in timezones_to_test:
            response = requests.get(f"{base_url}?timezone={tz}", timeout=5)
            if response.status_code == 200:
                print(f"✅ Affichage réussi pour {tz}")
                # Vérifier que le fuseau horaire est affiché
                if f'<span id="current-timezone">{tz}</span>' in response.text:
                    print(f"  ✅ Fuseau horaire {tz} correctement affiché")
                else:
                    print(f"  ⚠️  Fuseau horaire {tz} peut-être pas affiché correctement")
            else:
                print(f"❌ Erreur d'affichage pour {tz} (code: {response.status_code})")
        
        print("\n=== Test de l'application web terminé ===")
        print("\nRésumé:")
        print("✅ L'application web fonctionne correctement")
        print("✅ Les produits peuvent être ajoutés")
        print("✅ L'affichage selon différents fuseaux horaires fonctionne")
        
    except requests.exceptions.ConnectionError:
        print("❌ Impossible de se connecter à l'application")
        print("Assurez-vous que l'application Flask est en cours d'exécution sur http://127.0.0.1:5001")
    except Exception as e:
        print(f"❌ Erreur lors du test: {e}")

if __name__ == "__main__":
    test_web_application()
